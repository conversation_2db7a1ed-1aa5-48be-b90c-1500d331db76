<template>
    <div class="main-periodCompareDataBoard-container">
        <Card>
            <template #header>
                <div class="card-header">
                    <div class="left">
                        <span class="card-title" style="z-index: 10;">周期业务数据对比</span>
                    </div>
                    <div class="right">
                        <FloatLabel variant="on">
                            <DatePicker v-model="Range1" inputId="on_label" showIcon iconDisplay="input"
                                selectionMode="range" :manualInput="false" size="small" />
                            <label for="on_label">周期1</label>
                        </FloatLabel>
                        <FloatLabel variant="on">
                            <DatePicker v-model="Range2" inputId="on_label" showIcon iconDisplay="input"
                                selectionMode="range" :manualInput="false" size="small" />
                            <label for="on_label">周期2</label>
                        </FloatLabel>
                        <Button class="download-button" icon="pi pi-undo" style="font-size: 1rem;" size="small"
                            @click="resetAllSelections" />
                        <Button class="download-button" icon="pi pi-search" style="font-size: 1rem;" size="small" />
                    </div>

                </div>
            </template>
            <template #content>
                <Splitter style="height: 27rem">
                    <SplitterPanel class="options-panel" :size="25" :minSize="10">
                        <div class="info">
                            <p>请选择要查看的业务数据项</p>
                        </div>
                        <div class="options-panel-content">
                            <Listbox v-model="selectedCity" :options="cities" optionLabel="name" class="listbox" />
                        </div>

                    </SplitterPanel>
                    <SplitterPanel class="data-panel" :size="75">
                        <DataTable :value="optionData" stripedRows tableStyle="min-width: 50rem"
                            scrollable scrollHeight="27rem">
                            <ColumnGroup type="header">
                                <Row>
                                    <Column header="业务人员" :rowspan="3" />
                                </Row>
                                <Row>
                                    <Column :header="selectedCity.name" :colspan="4" />
                                </Row>
                                <Row>
                                    <Column header="周期1" sortable field="lastYearSale" />
                                    <Column header="周期2" sortable field="thisYearSale" />
                                    <Column header="差值（周期1-周期2）" sortable field="diff" />
                                </Row>
                            </ColumnGroup>
                            <Column field="product" />
                            <Column field="lastYearSale" />
                            <Column field="thisYearSale" />
                            <!-- 当差值是负数时，diffCloumnColor 样式会应用 red-text 样式，否则应用 green-text 样式 -->
                            <Column field="diff">
                                <template #body="slotProps">
                                    <span :class="getDiffColumnColor(slotProps.data.diff)">
                                        {{ slotProps.data.diff }}
                                    </span>
                                </template>
                            </Column>
                            <ColumnGroup type="footer">
                                <Row>
                                    <Column footer="总计:" />
                                    <Column :footer=lastYearTotal footerStyle="text-align:left" />
                                    <Column :footer=thisYearTotal footerStyle="text-align:left" />
                                    <Column :footer=diffTotal footerStyle="text-align:left" />
                                </Row>
                            </ColumnGroup>
                        </DataTable>
                    </SplitterPanel>
                </Splitter>

            </template>
        </Card>
    </div>

</template>
<script setup>
import Card from 'primevue/card';
import FloatLabel from 'primevue/floatlabel';
import DatePicker from 'primevue/datepicker';
import Button from 'primevue/button';

import Splitter from 'primevue/splitter';
import SplitterPanel from 'primevue/splitterpanel';

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import ColumnGroup from 'primevue/columngroup';
import Row from 'primevue/row';


import Listbox from 'primevue/listbox';

import { ref, computed } from "vue";

// 日期选择器变量
const Range1 = ref();
const Range2 = ref();

//模拟数据-周期对比面板的业务数据选项
const selectedCity = ref({ name: '新增客户数', code: 'ACUS' });
const cities = ref([
    { name: '新增客户数', code: 'ACUS' },
    { name: '新增权益（仅入金）', code: 'APROF' },
    { name: '新增净权益（入金-出金）', code: 'ANPROF' },
    { name: '新增手续费', code: 'AST' }
]);



const lastYearTotal = computed(() => {
    let total = 0;
    for (let sale of optionData.value) {
        total += sale.lastYearSale;
    }

    return parseFloat(total.toFixed(2));
});

const thisYearTotal = computed(() => {
    let total = 0;
    for (let sale of optionData.value) {
        total += sale.thisYearSale;
    }

    return parseFloat(total.toFixed(2));
});

const diffTotal = computed(() => {
    let total = 0;
    for (let sale of optionData.value) {
        total += sale.diff;
    }

    return parseFloat(total.toFixed(2));
});

// 根据差值返回对应的样式类名
const getDiffColumnColor = (diffValue) => {
    return diffValue < 0 ? 'red-text' : 'green-text';
};

// 重置所有选择的函数
const resetAllSelections = () => {
    Range1.value = null;
    Range2.value = null;
};

//根据不同的选择项来展示对应的模拟数据
const optionData = computed(() => {

    let optionData = [];
    if (selectedCity.value?.code === 'ACUS') {
        optionData = [
            { product: '林砚秋', lastYearSale: 10, thisYearSale: 17, diff: 10 - 17 },
            { product: '顾明宇', lastYearSale: 5, thisYearSale: 10, diff: 5 - 10 },
            { product: '苏晚柠', lastYearSale: 38, thisYearSale: 5, diff: 38 - 5 },
            { product: '陈景行', lastYearSale: 7, thisYearSale: 7, diff: 7 - 7 },
            { product: '孟星辞', lastYearSale: 17, thisYearSale: 10, diff: 17 - 10 },
            { product: '周砚舟', lastYearSale: 29, thisYearSale: 33, diff: 29 - 33 },
            { product: '周砚舟', lastYearSale: 10, thisYearSale: 7, diff: 10 - 7 },
            { product: '夏知遥', lastYearSale: 27, thisYearSale: 33, diff: 27 - 33 },
            { product: '陆承宇', lastYearSale: 22, thisYearSale: 11, diff: 22 - 11 },
            { product: '沈叙白', lastYearSale: 51, thisYearSale: 12, diff: 51 - 12 }
        ];
    } else if (selectedCity.value?.code === 'APROF') {
        optionData = [
            { product: '林砚秋', lastYearSale: 12500.50, thisYearSale: 17800.80, diff: parseFloat((12500.50 - 17800.80).toFixed(2)) },
            { product: '顾明宇', lastYearSale: 5200.20, thisYearSale: 10350.35, diff: parseFloat((5200.20 - 10350.35).toFixed(2)) },
            { product: '苏晚柠', lastYearSale: 38600.60, thisYearSale: 5750.75, diff: parseFloat((38600.60 - 5750.75).toFixed(2)) },
            { product: '陈景行', lastYearSale: 71000.10, thisYearSale: 71000.10, diff: parseFloat((71000.10 - 71000.10).toFixed(2)) },
            { product: '孟星辞', lastYearSale: 17900.90, thisYearSale: 10250.25, diff: parseFloat((17900.90 - 10250.25).toFixed(2)) },
            { product: '周砚舟', lastYearSale: 294000.40, thisYearSale: 336500.65, diff: parseFloat((294000.40 - 336500.65).toFixed(2)) },
            { product: '周砚舟', lastYearSale: 10800.80, thisYearSale: 7500.50, diff: parseFloat((10800.80 - 7500.50).toFixed(2)) },
            { product: '夏知遥', lastYearSale: 27300.30, thisYearSale: 33900.90, diff: parseFloat((27300.30 - 33900.90).toFixed(2)) },
            { product: '陆承宇', lastYearSale: 226000.60, thisYearSale: 114500.45, diff: parseFloat((226000.60 - 114500.45).toFixed(2)) },
            { product: '沈叙白', lastYearSale: 512500.25, thisYearSale: 128000.80, diff: parseFloat((512500.25 - 128000.80).toFixed(2)) }
        ];
    } else if (selectedCity.value?.code === 'ANPROF') {
        optionData = [
            { product: '林砚秋', lastYearSale: 45200.75, thisYearSale: 52800.40, diff: parseFloat((45200.75 - 52800.40).toFixed(2)) },
            { product: '顾明宇', lastYearSale: 89600.30, thisYearSale: 75300.65, diff: parseFloat((89600.30 - 75300.65).toFixed(2)) },
            { product: '苏晚柠', lastYearSale: 125800.90, thisYearSale: 142600.20, diff: parseFloat((125800.90 - 142600.20).toFixed(2)) },
            { product: '陈景行', lastYearSale: 36400.15, thisYearSale: 39800.80, diff: parseFloat((36400.15 - 39800.80).toFixed(2)) },
            { product: '孟星辞', lastYearSale: 215700.50, thisYearSale: 198300.75, diff: parseFloat((215700.50 - 198300.75).toFixed(2)) },
            { product: '周砚舟', lastYearSale: 68900.40, thisYearSale: 82500.30, diff: parseFloat((68900.40 - 82500.30).toFixed(2)) },
            { product: '周砚舟', lastYearSale: 156200.85, thisYearSale: 149700.60, diff: parseFloat((156200.85 - 149700.60).toFixed(2)) },
            { product: '夏知遥', lastYearSale: 94300.25, thisYearSale: 108600.50, diff: parseFloat((94300.25 - 108600.50).toFixed(2)) },
            { product: '陆承宇', lastYearSale: 287500.60, thisYearSale: 312800.90, diff: parseFloat((287500.60 - 312800.90).toFixed(2)) },
            { product: '沈叙白', lastYearSale: 421900.75, thisYearSale: 398600.40, diff: parseFloat((421900.75 - 398600.40).toFixed(2)) }
        ];
    } else if (selectedCity.value?.code === 'AST') {
        optionData = [
            { product: '林砚秋', lastYearSale: 67800.25, thisYearSale: 72500.60, diff: parseFloat((67800.25 - 72500.60).toFixed(2)) },
            { product: '顾明宇', lastYearSale: 135200.80, thisYearSale: 129800.45, diff: parseFloat((135200.80 - 129800.45).toFixed(2)) },
            { product: '苏晚柠', lastYearSale: 52300.70, thisYearSale: 68900.90, diff: parseFloat((52300.70 - 68900.90).toFixed(2)) },
            { product: '陈景行', lastYearSale: 218500.30, thisYearSale: 205700.65, diff: parseFloat((218500.30 - 205700.65).toFixed(2)) },
            { product: '孟星辞', lastYearSale: 89600.50, thisYearSale: 94200.20, diff: parseFloat((89600.50 - 94200.20).toFixed(2)) },
            { product: '周砚舟', lastYearSale: 342700.85, thisYearSale: 368900.50, diff: parseFloat((342700.85 - 368900.50).toFixed(2)) },
            { product: '周砚舟', lastYearSale: 76400.15, thisYearSale: 71200.80, diff: parseFloat((76400.15 - 71200.80).toFixed(2)) },
            { product: '夏知遥', lastYearSale: 189300.60, thisYearSale: 201500.30, diff: parseFloat((189300.60 - 201500.30).toFixed(2)) },
            { product: '陆承宇', lastYearSale: 95700.40, thisYearSale: 88900.75, diff: parseFloat((95700.40 - 88900.75).toFixed(2)) },
            { product: '沈叙白', lastYearSale: 486200.90, thisYearSale: 512700.25, diff: parseFloat((486200.90 - 512700.25).toFixed(2)) }
        ];
    }

    return optionData;
});







</script>
<style>
@layer reset, primevue, custom;

@layer custom {
    .main-periodCompareDataBoard-container {
        /* background-color: bisque; */
        /* height: 800px; */
    }

    .main-periodCompareDataBoard-container .card-header {
        display: flex;
        flex-direction: row;
        align-items: center;

        width: 100%;
    }

    .main-periodCompareDataBoard-container .card-header .left {
        /* background-color: antiquewhite; */

        width: 40%;

        font-size: 1.3rem;
        font-weight: bold;
        letter-spacing: 0.08rem;

        padding: 0.4rem;
        padding-left: 0.7rem;
    }

    .main-periodCompareDataBoard-container .card-header .right {
        /* background-color: rgb(209, 165, 106); */

        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        gap: 0.8rem;

        width: 60%;

        padding: 0.4rem;
    }

    .main-periodCompareDataBoard-container .card-header .right .download-button {
        background-color: #202020;
        border: none;
    }

    .main-periodCompareDataBoard-container .options-panel {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .main-periodCompareDataBoard-container .data-panel {
        /* padding:1rem */
    }

    .main-periodCompareDataBoard-container .options-panel .info {
        color: #6a6b6e;
    }

    .main-periodCompareDataBoard-container .options-panel .options-panel-content {
        /* background-color: antiquewhite; */
        width: 100%;
        height: 100%;
    }

    .main-periodCompareDataBoard-container .options-panel .options-panel-content .listbox {
        height: 100%;
        border-left: none;
        border-right: none;
        /* display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center; */
    }


    /* 差值列的样式 */
    .red-text {
        color: #e74c3c;
        font-weight: bold;
    }

    .green-text {
        color: #27ae60;
        font-weight: bold;
    }

}
</style>
