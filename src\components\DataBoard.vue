<template>
    <div class="main-periodCompareDataBoard-container">
        <Card class="main-periodCompareDataBoard-card">
            <template #header>
                <div class="card-header">
                    <div class="left">
                        <span class="card-title" style="z-index: 10;">周期业务数据明细</span>
                    </div>
                    <div class="right">
                        <Select v-model="selectedPeriod" :options="Period" optionLabel="name" placeholder="快捷区间查询"
                            class="w-full md:w-56" size="small" />

                        <FloatLabel variant="on">
                            <DatePicker v-model="Range1" inputId="start_date" showIcon iconDisplay="input" size="small"
                                :disabled="isDatePickerDisabled" />
                            <label for="start_date">开始时间</label>
                        </FloatLabel>
                        <FloatLabel variant="on">
                            <DatePicker v-model="Range2" inputId="end_date" showIcon iconDisplay="input" size="small"
                                :disabled="isDatePickerDisabled" />
                            <label for="end_date">结束时间</label>
                        </FloatLabel>
                        <Button class="download-button" icon="pi pi-undo" style="font-size: 1rem;" size="small"
                            @click="resetAllSelections" />
                        <Button class="download-button" icon="pi pi-search" style="font-size: 1rem;" size="small" />
                    </div>

                </div>
            </template>
            <template #content>
                <DataTable :value="sales" stripedRows tableStyle="min-width: 50rem" scrollable scrollHeight="27rem">
                    <ColumnGroup type="header">
                        <Row>
                            <Column header="业务人员" />
                            <Column header="新增客户数" sortable field="addCustomer"/>
                            <Column header="总客户数" sortable field="totalCustomer"/>
                            <Column header="新增权益（仅入金）" sortable field="addEquity"/>
                            <Column header="新增净权益（入金-出金）" sortable field="addNetEquity"/>
                            <Column header="总权益" sortable field="totalEquity"/>
                            <Column header="新增手续费" sortable field="addFee"/>
                            <Column header="总手续费" sortable field="totalFee"/>
                        </Row>
                    </ColumnGroup>
                    <Column field="product" header="业务人员" />
                    <Column field="addCustomer" header="新增客户数" />
                    <Column field="totalCustomer" header="总客户数" />
                    <Column field="addEquity" header="新增权益（仅入金）" />
                    <Column field="addNetEquity" header="新增净权益（入金-出金）" />
                    <Column field="totalEquity" header="总权益" />
                    <Column field="addFee" header="新增手续费" />
                    <Column field="totalFee" header="总手续费" />

                    <ColumnGroup type="footer">
                        <Row>
                            <Column footer="总计:" footerStyle="text-align: left;" />
                            <Column :footer=Sum_addCustomer footerStyle="text-align: left;" />
                            <Column :footer=Sum_totalCustomer footerStyle="text-align: left;" />
                            <Column :footer=Sum_addEquity footerStyle="text-align: left;" />
                            <Column :footer=Sum_addNetEquity footerStyle="text-align: left;" />
                            <Column :footer=Sum_totalEquity footerStyle="text-align: left;" />
                            <Column :footer=Sum_addFee footerStyle="text-align: left;" />
                            <Column :footer=Sum_totalFee footerStyle="text-align: left;" />

                        </Row>
                    </ColumnGroup>
                </DataTable>
            </template>
        </Card>
    </div>

</template>
<script setup>
import Card from 'primevue/card';
import FloatLabel from 'primevue/floatlabel';
import DatePicker from 'primevue/datepicker';
import Button from 'primevue/button';
import Select from 'primevue/select';


import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import ColumnGroup from 'primevue/columngroup';
import Row from 'primevue/row';



import { ref, computed, watch } from "vue";

// 日期选择器变量
const Range1 = ref();
const Range2 = ref();

//模拟数据-周期业务数据
const sales = ref([
    { product: '林砚秋', addCustomer: 10, totalCustomer: 108, addEquity: -1592611.99, addNetEquity: 6685099.85, totalEquity: 7885099.85, addFee: 680075.23, totalFee: 1310075.23 },
    { product: '顾明宇', addCustomer: 6, totalCustomer: 86, addEquity: 987245.32, addNetEquity: 4123560.78, totalEquity: 5210806.10, addFee: 421560.35, totalFee: 832750.67 },
    { product: '苏晚柠', addCustomer: 26, totalCustomer: 156, addEquity: 2156890.45, addNetEquity: 9236540.21, totalEquity: 11393430.66, addFee: 942870.56, totalFee: 1856320.89 },
    { product: '陈景行', addCustomer: 7, totalCustomer: 95, addEquity: -876540.23, addNetEquity: 3689210.75, totalEquity: 4565750.98, addFee: 378540.62, totalFee: 742150.38 },
    { product: '孟星辞', addCustomer: 13, totalCustomer: 120, addEquity: -1356890.76, addNetEquity: 5689210.34, totalEquity: 7046101.10, addFee: 582140.75, totalFee: 1143680.92 },
    { product: '周砚舟', addCustomer: 23, totalCustomer: 145, addEquity: 1987650.43, addNetEquity: 8326540.91, totalEquity: 10314191.34, addFee: 853260.47, totalFee: 1678420.63 },
    { product: '夏知遥', addCustomer: 2, totalCustomer: 68, addEquity: -456210.89, addNetEquity: 1897650.32, totalEquity: 2353861.21, addFee: 195320.68, totalFee: 382650.45 },
    { product: '陆承宇', addCustomer: 9, totalCustomer: 102, addEquity: 1245780.65, addNetEquity: 5236890.47, totalEquity: 6482671.12, addFee: 536240.89, totalFee: 1053680.74 },
    { product: '江念初', addCustomer: 15, totalCustomer: 128, addEquity: 1568920.37, addNetEquity: 6547890.23, totalEquity: 8116810.60, addFee: 672450.32, totalFee: 1321680.59 },
    { product: '沈叙白', addCustomer: 10, totalCustomer: 112, addEquity: 1423560.89, addNetEquity: 5987650.43, totalEquity: 7411211.32, addFee: 612580.47, totalFee: 1198750.63 }
]);


//模拟数据-快捷区间选项
const selectedPeriod = ref();
const Period = ref([
    { name: '近1周', code: 'NY' },
    { name: '近1月', code: 'RM' },
    { name: '近3月', code: 'LDN' },
]);

// 计算属性：判断日期选择器是否应该被禁用
const isDatePickerDisabled = computed(() => {
    return selectedPeriod.value !== null && selectedPeriod.value !== undefined;
});

// 监听快捷区间选择的变化
watch(selectedPeriod, (newValue) => {
    if (newValue) {
        // 当选择了快捷区间时，重置日期选择器
        Range1.value = null;
        Range2.value = null;
    }
});

// 重置所有选择的函数
const resetAllSelections = () => {
    selectedPeriod.value = null;
    Range1.value = null;
    Range2.value = null;
};

const Sum_addCustomer = computed(() => {
    let sum = 0;
    sales.value.forEach(item => {
        sum += item.addCustomer;
    });
    return parseFloat(sum.toFixed(2));
});

const Sum_totalCustomer = computed(() => {
    let sum = 0;
    sales.value.forEach(item => {
        sum += item.totalCustomer;
    });
    return parseFloat(sum.toFixed(2));
});

const Sum_addEquity = computed(() => {
    let sum = 0;
    sales.value.forEach(item => {
        sum += item.addEquity;
    });
    return parseFloat(sum.toFixed(2));
});

const Sum_addNetEquity = computed(() => {
    let sum = 0;
    sales.value.forEach(item => {
        sum += item.addNetEquity;
    });
    return parseFloat(sum.toFixed(2));
});

const Sum_totalEquity = computed(() => {
    let sum = 0;
    sales.value.forEach(item => {
        sum += item.totalEquity;
    });
    return parseFloat(sum.toFixed(2));
});

const Sum_addFee = computed(() => {
    let sum = 0;
    sales.value.forEach(item => {
        sum += item.addFee;
    });
    return parseFloat(sum.toFixed(2));
});

const Sum_totalFee = computed(() => {
    let sum = 0;
    sales.value.forEach(item => {
        sum += item.totalFee;
    });
    return parseFloat(sum.toFixed(2));
});







</script>
<style>
@layer reset, primevue, custom;

@layer custom {
    .main-periodCompareDataBoard-container {
        /* background-color: bisque; */
        /* height: 800px; */
    }

    .main-periodCompareDataBoard-card {
        border: #6a6b6e;
    }



    .main-periodCompareDataBoard-container .card-header {
        display: flex;
        flex-direction: row;
        align-items: center;

        position: relative;

        width: 100%;
    }

    .main-periodCompareDataBoard-container .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('@/assets/coolbackgrounds-topography-micron.svg');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        transform: scaleX(-1);
        z-index: 0;
    }

    .main-periodCompareDataBoard-container .card-header .left {
        /* background-color: antiquewhite; */

        width: 40%;

        font-size: 1.3rem;
        font-weight: bold;
        letter-spacing: 0.08rem;

        padding: 0.4rem;
        padding-left: 0.7rem;
    }

    .main-periodCompareDataBoard-container .card-header .right {
        /* background-color: rgb(209, 165, 106); */

        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        gap: 0.8rem;

        width: 60%;

        padding: 0.4rem;
    }

    .main-periodCompareDataBoard-container .card-header .right .download-button {
        background-color: #202020;
        border: none;
    }




}
</style>
