import { createApp } from 'vue';
import App from './LoginPage.vue';
import PrimeVue from 'primevue/config';
import Aura from '@primeuix/themes/aura';
import Particles from "@tsparticles/vue3";
import { loadSlim } from "@tsparticles/slim";

const app = createApp(App);

app.use(PrimeVue, {
    theme: {
        preset: Aura,
        options: {
            prefix: 'p',
            darkModeSelector: 'system',
            cssLayer: true
        }
    }
});

app.use(Particles, {
    init: async (engine) => {
        await loadSlim(engine);
    }
});

app.mount('#app');