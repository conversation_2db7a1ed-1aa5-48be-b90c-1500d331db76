<template>
    <div class="main-page-container">
        <div class="left">
            <SideBar />
        </div>
        <div class="right">
            <MainOperationArea v-if="sideBar_Function_Activated === 'home'"/>

            <!-- 只有当sideBar_Function_Activated值为home时才显示PeriodCompareDataBoard、DataBoard -->
            <PeriodCompareDataBoard v-if="sideBar_Function_Activated === 'home'" />
            <DataBoard v-if="sideBar_Function_Activated === 'home'" />

            <UpLoadBoard v-if="sideBar_Function_Activated === 'upload'"/>
        </div>
    </div>

</template>


<script setup>
import SideBar from './components/SideBar.vue'
import MainOperationArea from './components/MainOperationArea.vue';
import PeriodCompareDataBoard from './components/PeriodCompareDataBoard.vue';
import DataBoard from './components/DataBoard.vue';
import UpLoadBoard from './components/UpLoadBoard.vue';

import { provide, ref } from 'vue';




// 响应式变量：当前激活的侧边栏功能
const sideBar_Function_Activated = ref("home");




provide("mainOperationArea_Title", "业务统计数据看板");
provide("sideBar_Function_Activated", sideBar_Function_Activated);


</script>



<style>
@layer reset, primevue, custom;

@layer custom {
    .main-page-container {
        display: flex;
        flex-direction: row;
        height: 100%;
        width: 100%;
        background-color: #ffffff;
    }

    .main-page-container .left {
        display: flex;
        flex-direction: row;
        width: 5.3rem;
        height: 100%;
    }

    .main-page-container .right {
        display: flex;
        flex-direction: column;
        gap: 1.8rem;

        height: 100%;
        width: 100%;

    }
}
</style>