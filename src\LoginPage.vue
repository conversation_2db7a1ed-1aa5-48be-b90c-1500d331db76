<template>
  <div class="particles-container">
    <!-- 粒子容器组件 -->
    <Particles 
      id="tsparticles" 
      :options="particlesOptions" 
      class="particles"
    />
    
    <!-- 可选的信息叠加层 -->
    <div class="info-overlay">
      <h2>3D粒子球体效果</h2>
      <p>使用tsparticles实现的旋转球体粒子效果</p>
    </div>
  </div>
</template>

<script setup>
// 定义粒子效果的配置选项
const particlesOptions = {
  // 基础设置
  fullScreen: {
    enable: true,
    zIndex: 1
  },
  background: {
    color: {
      value: "#0a0a16" // 深色背景，增强粒子可见性
    }
  },
  
  // 帧率控制
  fpsLimit: 60,
  
  // 交互设置
  interactivity: {
    events: {
      onClick: {
        enable: true,
        mode: "push" // 点击时添加粒子
      },
      onHover: {
        enable: true,
        mode: "grab" // 悬停时粒子被吸引
      },
      resize: true
    },
    modes: {
      push: {
        quantity: 5 // 每次点击添加5个粒子
      },
      grab: {
        distance: 150 // 吸引距离
      }
    }
  },
  
  // 粒子设置
  particles: {
    color: {
      value: ["#4CAF50", "#2196F3", "#FFC107", "#E91E63"] // 粒子颜色数组
    },
    links: {
      color: {
        value: "#ffffff" // 连接线颜色
      },
      distance: 100, // 连接距离
      enable: true, // 启用连接线
      opacity: 0.2, // 连接线透明度
      width: 1 // 连接线宽度
    },
    move: {
      enable: true,
      speed: 0.5, // 粒子移动速度
      direction: "none",
      random: true,
      straight: false,
      outModes: {
        default: "out"
      },
      attract: {
        enable: true,
        rotateX: 600, // X轴吸引力
        rotateY: 1200 // Y轴吸引力
      }
    },
    number: {
      density: {
        enable: true,
        area: 800 // 粒子密度区域
      },
      value: 100 // 粒子数量
    },
    opacity: {
      value: 0.5, // 粒子透明度
      random: true
    },
    shape: {
      type: "circle" // 粒子形状
    },
    size: {
      value: { min: 1, max: 3 }, // 粒子大小范围
      random: true
    }
  },
  
  // 3D效果设置
  detectRetina: true,
  preset: "ball", // 使用球体预设
  // 动画效果 - 让球体旋转
  animation: {
    enable: true,
    speed: 1,
    mode: "rotate", // 旋转模式
    directions: ["x", "y", "z"], // 在三个轴上都旋转
    random: false
  }
};
</script>

<style scoped>
.particles-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.info-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  text-align: center;
  color: white;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

p {
  font-size: 1.2rem;
  opacity: 0.8;
}
</style>
